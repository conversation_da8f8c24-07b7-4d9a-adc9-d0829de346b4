/**
 * 编辑器主页面
 * 采用经典的四区域布局：顶部工具栏 + 左侧工具栏 + 中间视口 + 右侧面板 + 底部资源面板
 */
import React, { useState } from 'react';
import { Layout, Tabs, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import EditorTopBar from '../../components/toolbar/EditorTopBar';
import LeftToolbar from '../../components/toolbar/LeftToolbar';
import Viewport from '../../components/Viewport';
import PropertiesPanel from '../../components/PropertiesPanel';
import HierarchyPanel from '../../components/panels/HierarchyPanel';
import ModelsPanel from '../../components/panels/ModelsPanel';
import MaterialLibraryPanel from '../../components/panels/MaterialLibraryPanel';
import BottomAssetsPanel from '../../components/panels/BottomAssetsPanel';
import './Editor.less';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;

const Editor: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [rightPanelTab, setRightPanelTab] = useState<string>('hierarchy');
  const [bottomPanelHeight, setBottomPanelHeight] = useState<number>(250);

  // 处理打开面板
  const handleOpenPanel = (panelType: string) => {
    console.log('打开面板:', panelType);
    message.info(`${t('editor.openPanel')}: ${panelType}`);
  };

  // 处理发布
  const handlePublish = () => {
    message.success(t('editor.publishSuccess') || '发布成功');
  };

  // 处理添加实体
  const handleAddEntity = () => {
    message.info(t('editor.addEntitySuccess') || '实体已添加');
  };

  // 处理工具切换
  const handleToolChange = (tool: string) => {
    console.log('切换工具:', tool);
  };

  return (
    <Layout className="editor-layout">
      {/* 顶部工具栏 */}
      <EditorTopBar
        onOpenPanel={handleOpenPanel}
        onPublish={handlePublish}
        onAddEntity={handleAddEntity}
      />

      {/* 主内容区域 */}
      <Layout className="editor-main">
        {/* 左侧工具栏和中间右侧区域 */}
        <Layout className="editor-horizontal">
          {/* 左侧工具栏 */}
          <LeftToolbar onToolChange={handleToolChange} />

          {/* 中间和右侧区域 */}
          <Layout className="editor-center-right">
            {/* 上部：中间视口和右侧面板的水平布局 */}
            <Layout className="editor-top-section">
              {/* 中间视口区域 */}
              <Content className="editor-viewport">
                <Viewport />
              </Content>

              {/* 右侧面板 - 分为上下两部分 */}
              <Sider width={300} className="editor-right-panel" theme="light">
                {/* 上部：标签页面板 */}
                <div className="right-panel-top">
                  <Tabs
                    activeKey={rightPanelTab}
                    onChange={setRightPanelTab}
                    className="right-panel-tabs"
                    size="small"
                  >
                    <TabPane
                      tab={t('editor.models') || 'Models'}
                      key="models"
                    >
                      <div className="panel-content">
                        {/* 显示场景中的模型列表 */}
                        <ModelsPanel />
                      </div>
                    </TabPane>
                    <TabPane
                      tab={t('editor.hierarchy') || 'Hierarchy'}
                      key="hierarchy"
                    >
                      <div className="panel-content">
                        <HierarchyPanel />
                      </div>
                    </TabPane>
                    <TabPane
                      tab={t('editor.materialLibrary') || 'Material Library'}
                      key="materials"
                    >
                      <div className="panel-content">
                        {/* 材质库内容 */}
                        <MaterialLibraryPanel />
                      </div>
                    </TabPane>
                  </Tabs>
                </div>

                {/* 下部：属性面板 */}
                <div className="right-panel-bottom">
                  <div className="properties-header">
                    <h4>{t('editor.properties') || 'Properties'}</h4>
                  </div>
                  <div className="properties-content">
                    <PropertiesPanel />
                  </div>
                </div>
              </Sider>
            </Layout>

            {/* 底部资源面板 - 只覆盖中间和右侧区域 */}
            <div
              className="editor-bottom-panel"
              style={{ height: bottomPanelHeight }}
            >
              <BottomAssetsPanel />
            </div>
          </Layout>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default Editor;
